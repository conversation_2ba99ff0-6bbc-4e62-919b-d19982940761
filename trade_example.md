// Ultra-Simplified Mobula WebSocket Trade Feed Client (target < 50 lines)
const WebSocket = require('ws');
require('dotenv').config();

const { MOBULA_API_KEY: API_KEY,
    RECONNECT_INTERVAL_MS: RECON_DELAY = 5000 } = process.env;

if (!API_KEY) { console.error('MOBULA_API_KEY missing in .env. Exiting.'); process.exit(1); }

let ws, reconAttempts = 0;
const MAX_RECON = 3; // Max 3 reconnect attempts for brevity


function connect() {
    ws = new WebSocket('wss://api.mobula.io');

    ws.onopen = () => {
        console.log('Connected.');
        reconAttempts = 0;
        const subMsg = {
            type: 'pair',
            authorization: 'fffa68cd-6bde-4ac5-909d-eb627d8baca0',
            payload: { 
                blockchain: 'solana', 
                address: 'EDFz66ADuofKgbZG9R8d8CdWn9z1FWaaxmWkf6DhycUS',

            } };
        try { ws.send(JSON.stringify(subMsg)); console.log('Subscribed.'); }
        catch (e) { console.error('Subscribe failed:', e.message); }
    };

    ws.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data.toString());
            const processTrade = async (trade) => {
                console.log('\n=== BONDING CURVE CALCULATION ===');
                
                if (trade.pairData && trade.pairData.token1) {
                    // Bonding curve constants based on Pump.fun documentation
                    const TOTAL_SUPPLY = 1000000000; // 1 billion tokens
                    const RESERVED_TOKENS = 206900000; // Reserved tokens not in bonding curve
                    const INITIAL_REAL_TOKEN_RESERVES = 793100000; // Tokens available for bonding curve
                    const GRADUATION_MARKET_CAP = 69000; // $69,000 graduation threshold
                    
                    // Get current token reserves and market cap
                    const currentTokenReserves = trade.pairData.token1?.approximateReserveToken || 0;
                    const currentMarketCap = trade.pairData.token1?.marketCap || 0;
                    const tokenPrice = trade.pairData.token1?.price || 0;
                    
                    // Calculate bonding curve progress using the documented formula
                    // BondingCurveProgress = 100 - (((balance - 206900000) * 100) / 793100000)
                    // Where balance is current token reserves
                    let bondingCurveProgress = 0;
                    if (currentTokenReserves > RESERVED_TOKENS) {
                        bondingCurveProgress = 100 - (((currentTokenReserves - RESERVED_TOKENS) * 100) / INITIAL_REAL_TOKEN_RESERVES);
                    } else {
                        // If reserves are below reserved amount, calculate based on market cap
                        bondingCurveProgress = Math.min((currentMarketCap / GRADUATION_MARKET_CAP) * 100, 100);
                    }
                    
                    const progressCapped = Math.max(0, Math.min(bondingCurveProgress, 100));
                    const remainingMarketCap = Math.max(0, GRADUATION_MARKET_CAP - currentMarketCap);
                    const solInPool = trade.pairData.token0?.approximateReserveToken || 0;
                    
                    console.log('🚀 BONDING CURVE PROGRESS:');
                    console.log(`Progress: ${progressCapped.toFixed(2)}%`);
                    console.log(`Current Market Cap: $${currentMarketCap.toLocaleString()}`);
                    console.log(`Graduation Threshold: $${GRADUATION_MARKET_CAP.toLocaleString()}`);
                    console.log(`Remaining to graduation: $${remainingMarketCap.toLocaleString()}`);
                    console.log(`SOL in pool: ${solInPool.toFixed(6)}`);
                    console.log(`Token reserves: ${currentTokenReserves.toLocaleString()}`);
                    
                    console.log('\n📊 Trade Data:');
                    console.log(`Market Cap: $${trade.pairData.token1?.marketCap?.toLocaleString() || 'N/A'}`);
                    console.log(`Token Price: $${trade.pairData.token1?.price?.toFixed(8) || 'N/A'}`);
                    console.log(`Total Liquidity: $${trade.pairData.liquidity?.toLocaleString() || 'N/A'}`);
                    console.log(`Trade Type: ${trade.type}`);
                    console.log(`Trade Size: $${Number(trade.token_amount_usd || 0).toFixed(2)}`);
                    
                    console.log('\n🔍 Raw Data Used:');
                    console.log(`- SOL Reserve (token0): ${solInPool}`);
                    console.log(`- Token Reserve (token1): ${currentTokenReserves.toLocaleString()}`);
                    console.log(`- Token Price: $${tokenPrice.toFixed(8)}`);
                    console.log(`- SOL Price: $${trade.pairData.token0?.price?.toFixed(2) || 'N/A'}`);
                    console.log(`- Calculation Method: ${currentTokenReserves > RESERVED_TOKENS ? 'Token Reserve Formula' : 'Market Cap Formula'}`);
                    
                } else {
                    console.log('❌ Cannot calculate bonding curve - missing token data');
                    console.log('Available data:', {
                        hasPairData: !!trade.pairData,
                        hasToken0: !!trade.pairData?.token0,
                        hasToken1: !!trade.pairData?.token1,
                        token0Symbol: trade.pairData?.token0?.symbol,
                        token1Symbol: trade.pairData?.token1?.symbol
                    });
                }
                
                console.log('\n' + '─'.repeat(60));
            };
            Array.isArray(data) ? data.forEach(processTrade) : processTrade(data);
        } catch (e) { console.error('Msg err:', e.message); }
    };

    ws.onclose = (e) => {
        console.log(`Closed. Code: ${e.code}${e.reason ? ' Reason: ' + e.reason : ''}`);
        if (reconAttempts < MAX_RECON && e.code !== 1000) { // Don't recon on manual close (1000)
            reconAttempts++;
            console.log(`Reconnect attempt ${reconAttempts}/${MAX_RECON} in ${RECON_DELAY / 1000}s...`);
            setTimeout(connect, RECON_DELAY);
        } else if (e.code !== 1000) console.error('Max reconnects or unrecoverable. Not retrying.');
    };
    ws.onerror = (err) => console.error('WS Error:', err.message || 'Unknown');
}

console.log('Starting Ultra-Simplified Mobula Feed...');
connect();

process.on('SIGINT', () => {
    console.log('\nClosing...');
    if (ws) { ws.removeAllListeners(); ws.close(1000, 'SIGINT Shutdown'); }
    process.exit(0);
});