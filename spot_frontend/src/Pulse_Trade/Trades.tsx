import React, { useState, useEffect, useMemo } from 'react';
import { ChevronUp, ChevronDown, DollarSign, RefreshCcw, ArrowLeftRight, Filter, User } from 'lucide-react';
import { useTradeWebSocket } from '../hooks/useTradeWebSocket';

const Trades = () => {
  const [activeTab, setActiveTab] = useState('TRADES');
  const [sortAsc, setSortAsc] = useState(true);
  const [showPrice, setShowPrice] = useState(false);
  const [showUSD, setShowUSD] = useState(false);
  const [poolAddress, setPoolAddress] = useState<string | null>(null);

  // Get pool address from localStorage
  useEffect(() => {
    const getPoolAddress = () => {
      try {
        const activePulseToken = localStorage.getItem('activePulseToken');
        if (activePulseToken) {
          const tokenData = JSON.parse(activePulseToken);
          const poolAddr = tokenData.pool_address;
          if (poolAddr && poolAddr !== poolAddress) {
            setPoolAddress(poolAddr);
            console.log('📊 Trades: Updated pool address:', poolAddr);
          }
        }
      } catch (error) {
        console.error('❌ Failed to get pool address from localStorage:', error);
      }
    };

    // Get initial pool address
    getPoolAddress();

    // Listen for changes to activePulseToken
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'activePulseToken') {
        getPoolAddress();
      }
    };

    // Listen for custom events when token changes
    const handlePulseDataChange = () => {
      getPoolAddress();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('pulseDataChanged', handlePulseDataChange);
    window.addEventListener('pulseTradeTokenChange', handlePulseDataChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('pulseDataChanged', handlePulseDataChange);
      window.removeEventListener('pulseTradeTokenChange', handlePulseDataChange);
    };
  }, [poolAddress]);

  // Use trade WebSocket hook
  const { tradeHistory, isConnected, isConnecting, error } = useTradeWebSocket(poolAddress || undefined);

  // Process trade data for display
  const processedTradeData = useMemo(() => {
    if (!tradeHistory || tradeHistory.length === 0) {
      return [];
    }

    return tradeHistory.map((trade) => {
      // Calculate age from timestamp
      const ageMs = Date.now() - trade.date;
      const ageMinutes = Math.floor(ageMs / (1000 * 60));
      const ageHours = Math.floor(ageMinutes / 60);
      const ageDays = Math.floor(ageHours / 24);

      let ageDisplay = '';
      if (ageDays > 0) {
        ageDisplay = `${ageDays}d`;
      } else if (ageHours > 0) {
        ageDisplay = `${ageHours}h`;
      } else {
        ageDisplay = `${Math.max(1, ageMinutes)}m`;
      }

      // Format amounts
      const solAmount = trade.token_amount_vs || 0;
      const usdAmount = trade.token_amount_usd || 0;

      // Get market cap and price from pairData
      const marketCap = trade.pairData?.token1?.marketCap || 0;
      const tokenPrice = trade.pairData?.token1?.price || trade.token_price || 0;

      // Format market cap
      let mcDisplay = '';
      if (marketCap >= 1_000_000) {
        mcDisplay = `$${(marketCap / 1_000_000).toFixed(2)}M`;
      } else if (marketCap >= 1_000) {
        mcDisplay = `$${(marketCap / 1_000).toFixed(2)}K`;
      } else {
        mcDisplay = `$${marketCap.toFixed(0)}`;
      }

      // Format price
      const priceDisplay = tokenPrice >= 0.01 ? `$${tokenPrice.toFixed(4)}` : `$${tokenPrice.toFixed(8)}`;

      // Truncate sender address for trader display
      const traderDisplay = trade.sender ? `${trade.sender.slice(0, 3)}...${trade.sender.slice(-3)}` : 'Unknown';

      return {
        amount: solAmount.toFixed(3),
        usdAmount: `$${usdAmount.toFixed(2)}`,
        mc: mcDisplay,
        price: priceDisplay,
        trader: traderDisplay,
        age: ageDisplay,
        type: trade.type,
        timestamp: trade.date,
        hash: trade.hash
      };
    });
  }, [tradeHistory]);

  // Mock data for DEV and YOU tabs (can be replaced with filtered real data later)
  const devData = processedTradeData.filter(() => false); // No dev trades for now
  const youData = processedTradeData.filter(() => false); // No user trades for now

  const getCurrentData = () => {
    let data = [];
    if (activeTab === 'DEV') {
      data = devData;
    } else if (activeTab === 'YOU') {
      data = youData;
    } else {
      data = processedTradeData;
    }

    // Sort by age
    return [...data].sort((a, b) => {
      return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
    });
  };

  return (
    <div className="text-white max-h-[100rem] font-mono text-sm ">
      {/* Header Tabs */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
  {/* Left side: TRADES */}
  <div>
    <button
      onClick={() => setActiveTab('TRADES')}
      className={`text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'TRADES' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      Trades
    </button>
  </div>

  {/* Right side: DEV and YOU */}
  <div className="flex space-x-6">
    <button
      onClick={() => setActiveTab('DEV')}
      className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'DEV' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      <Filter size={18} className="mr-1" />
      DEV
    </button>

    <button
      onClick={() => setActiveTab('YOU')}
      className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'YOU' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      <User size={18} className="mr-1" />
      YOU
    </button>
  </div>
</div>

      {/* Column Headers */}
      <div className="flex items-center px-4 py-3 text-gray-400 text-xs border-b border-gray-800">
        {/* Amount / USD Toggle */}
        <div className="flex-1 flex items-center space-x-2">
          <span>Amount</span>
          <button
            onClick={() => setShowUSD(!showUSD)}
            className={`w-5 h-5 flex items-center justify-center rounded-full border transition-transform ${
              showUSD ? 'border-green-500 text-green-400' : 'border-gray-500 text-gray-400'
            } hover:scale-110`}
            title="Toggle USD/SOL"
          >
            <DollarSign size={10} />
          </button>
        </div>

        {/* Market Cap / Price Toggle */}
        <div className="w-24 flex items-center space-x-1 pl-4">
          <span>{showPrice ? 'Price' : 'MC'}</span>
          <button
  onClick={() => setShowPrice(!showPrice)}
  className="hover:text-white transition-colors"
  title="Toggle MC / Price"
>
  <ArrowLeftRight size={14} />
</button>

        </div>

        {/* Trader */}
        <div className="w-20 text-center">Trader</div>

        {/* Age Sort */}
        <div
          className="w-16 flex items-center justify-end cursor-pointer"
          onClick={() => setSortAsc(!sortAsc)}
        >
          <span>Age</span>
          {sortAsc ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
        </div>
      </div>

      {/* Trade Entries */}
      <div className="divide-y divide-gray-800">
        {/* Connection Status */}
        {!poolAddress && (
          <div className="flex items-center justify-center py-8 text-gray-400">
            <div className="text-center">
              <p className="text-sm">No token selected</p>
              <p className="text-xs mt-1">Select a token to view trades</p>
            </div>
          </div>
        )}

        {poolAddress && isConnecting && (
          <div className="flex items-center justify-center py-8 text-gray-400">
            <div className="text-center">
              <RefreshCcw className="animate-spin mx-auto mb-2" size={20} />
              <p className="text-sm">Connecting to trade feed...</p>
            </div>
          </div>
        )}

        {poolAddress && error && (
          <div className="flex items-center justify-center py-8 text-red-400">
            <div className="text-center">
              <p className="text-sm">Failed to connect to trade feed</p>
              <p className="text-xs mt-1">{error}</p>
            </div>
          </div>
        )}

        {poolAddress && isConnected && getCurrentData().length === 0 && (
          <div className="flex items-center justify-center py-8 text-gray-400">
            <div className="text-center">
              <p className="text-sm">No trades yet</p>
              <p className="text-xs mt-1">Waiting for trade activity...</p>
            </div>
          </div>
        )}

        {/* Trade Data */}
        {getCurrentData().map((trade, index) => (
          <div
            key={trade.hash || index}
            className="flex items-center px-4 py-2 hover:bg-gray-800/50 transition-colors"
            title={`Transaction: ${trade.hash || 'Unknown'}`}
          >
            <div
              className={`flex-1 font-medium flex items-center space-x-1 ${
                trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
              }`}
            >
              {!showUSD && (
                <img
                  src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                  alt="sol"
                  className="w-3 h-3"
                />
              )}
              <span>{showUSD ? trade.usdAmount : trade.amount}</span>
            </div>
            <div className="w-24 text-gray-300 font-medium pl-4">{showPrice ? trade.price : trade.mc}</div>
            <div className="w-20 text-center text-gray-300 font-medium">{trade.trader}</div>
            <div className="w-16 text-right text-gray-400">{trade.age}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Trades;
