import { useState, useMemo, useEffect } from 'react';
import { ChevronDown, TrendingUp, TrendingDown, Settings, RefreshCcw } from 'lucide-react';
import { useTradeWebSocket } from '../../hooks/useTradeWebSocket';

interface TradeData {
  id: string;
  age: string;
  tipAndPrio: { tip: number; priority: 'High' | 'Medium' | 'Low' };
  side: 'Buy' | 'Sell';
  priceSOL: number;
  amount: number;
  totalUSD: number;
  totalSOL: number;
  makerTxn: string;
  trader: 'Dev' | 'You' | 'Other';
}
type FilterType = 'All' | 'Dev' | 'You';

interface TradersProps {
  view?: 'dev' | 'you';
}

const Traders: React.FC<TradersProps> = ({ view: _view }) => {
  const [activeFilter] = useState<FilterType>('All');
  const [poolAddress, setPoolAddress] = useState<string | null>(null);

  // Get pool address from localStorage
  useEffect(() => {
    const getPoolAddress = () => {
      try {
        const activePulseToken = localStorage.getItem('activePulseToken');
        if (activePulseToken) {
          const tokenData = JSON.parse(activePulseToken);
          const poolAddr = tokenData.pool_address;
          if (poolAddr && poolAddr !== poolAddress) {
            setPoolAddress(poolAddr);
            console.log('📊 Traders: Updated pool address:', poolAddr);
          }
        }
      } catch (error) {
        console.error('❌ Failed to get pool address from localStorage:', error);
      }
    };

    // Get initial pool address
    getPoolAddress();

    // Listen for changes to activePulseToken
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'activePulseToken') {
        getPoolAddress();
      }
    };

    // Listen for custom events when token changes
    const handlePulseDataChange = () => {
      getPoolAddress();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('pulseDataChanged', handlePulseDataChange);
    window.addEventListener('pulseTradeTokenChange', handlePulseDataChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('pulseDataChanged', handlePulseDataChange);
      window.removeEventListener('pulseTradeTokenChange', handlePulseDataChange);
    };
  }, [poolAddress]);

  // Use trade WebSocket hook
  const { tradeHistory, isConnected, isConnecting, error } = useTradeWebSocket(poolAddress || undefined);

  // Process trade data for display
  const processedTrades = useMemo(() => {
    if (!tradeHistory || tradeHistory.length === 0) {
      return [];
    }

    return tradeHistory.map((trade, index) => {
      // Calculate age from timestamp
      const ageMs = Date.now() - trade.date;
      const ageMinutes = Math.floor(ageMs / (1000 * 60));
      const ageHours = Math.floor(ageMinutes / 60);
      const ageDays = Math.floor(ageHours / 24);

      let ageDisplay = '';
      if (ageDays > 0) {
        ageDisplay = `${ageDays}d`;
      } else if (ageHours > 0) {
        ageDisplay = `${ageHours}h`;
      } else {
        ageDisplay = `${Math.max(1, ageMinutes)}m`;
      }

      // Get SOL price from pairData
      const solPrice = trade.pairData?.token0?.price || 149; // Fallback SOL price

      // Calculate amounts
      const solAmount = trade.token_amount_vs || 0;
      const usdAmount = trade.token_amount_usd || 0;
      const tokenPrice = trade.pairData?.token1?.price || trade.token_price || 0;

      // Simulate tip and priority (in real implementation, this would come from transaction data)
      const tipAmount = solAmount * 0.001; // 0.1% tip simulation
      const priority = usdAmount > 1000 ? 'High' : usdAmount > 100 ? 'Medium' : 'Low';

      // Determine trader type (simplified - in real implementation, you'd check against known addresses)
      const traderType = 'Other'; // Could be enhanced to detect Dev/You based on wallet addresses

      // Truncate transaction hash
      const shortTxn = trade.hash ? `${trade.hash.slice(0, 8)}...${trade.hash.slice(-4)}` : 'Unknown';

      return {
        id: trade.hash || `trade-${index}`,
        age: ageDisplay,
        tipAndPrio: { tip: tipAmount, priority: priority as 'High' | 'Medium' | 'Low' },
        side: (trade.type === 'buy' ? 'Buy' : 'Sell') as 'Buy' | 'Sell',
        priceSOL: tokenPrice * solPrice, // Convert token price to SOL equivalent
        amount: solAmount,
        totalUSD: usdAmount,
        totalSOL: solAmount,
        makerTxn: shortTxn,
        trader: traderType as 'Dev' | 'You' | 'Other'
      };
    });
  }, [tradeHistory]);

  // ---------- helpers ----------
  const filteredTrades = useMemo(
    () => (activeFilter === 'All' ? processedTrades : processedTrades.filter(t => t.trader === activeFilter)),
    [activeFilter, processedTrades],
  );

  const fmtUsd = (v: number) =>
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2 }).format(v);

  const priorityCls = (p: string) =>
    ({
      High: 'text-red-400 bg-red-500/10 border-red-500/20',
      Medium: 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20',
      Low: 'text-green-400 bg-green-500/10 border-green-500/20',
    } as const)[p];

  const sideCls = (s: string) => (s === 'Buy' ? 'text-green-400' : 'text-red-400');
  const sideIcon = (s: string) => (s === 'Buy' ? <TrendingUp size={14} /> : <TrendingDown size={14} />);
  const badgeCls = (t: string) =>
    ({
      You: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      Dev: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
      Other: 'bg-gray-500/20 text-gray-300 border-gray-500/30',
    } as const)[t];

  // ---------- UI ----------
  return (
<div className="rounded-lg overflow-hidden">
  {/* Header */}
  <div className="overflow-x-auto">
    <table className="w-full">
      <thead>
        <tr className="border-b border-neutral-700 text-sm font-medium text-neutral-300">
          <th className="px-6 py-3 text-left min-w-[120px]">Age</th>
          <th className="px-6 py-3 text-left min-w-[100px]">Tip & Prio</th>
          <th className="px-6 py-3 text-left min-w-[80px]">Side</th>
          <th className="px-6 py-3 text-left min-w-[100px]">
            <div className="flex items-center gap-1">Price $ <ChevronDown size={12} /></div>
          </th>
          <th className="px-6 py-3 text-left min-w-[100px]">Amount</th>
          <th className="px-6 py-3 text-left min-w-[120px]">
            <div className="flex items-center gap-1">Total USD <ChevronDown size={12} /></div>
          </th>
          <th className="px-6 py-3 text-left min-w-[100px]">Total SOL</th>
          <th className="px-6 py-3 text-left min-w-[140px]">
            <div className="flex items-center gap-1">Maker Txn <Settings size={12} /></div>
          </th>
        </tr>
      </thead>
    </table>
  </div>

  {/* Body */}
  <div className="max-h-[400px] overflow-y-auto overflow-x-auto">
    <table className="w-full">
      <tbody className="divide-y divide-neutral-800/50">
        {/* Connection Status */}
        {!poolAddress && (
          <tr>
            <td colSpan={8} className="px-6 py-8 text-center">
              <div className="text-neutral-400">
                <p className="text-sm">No token selected</p>
                <p className="text-xs mt-1">Select a token to view trades</p>
              </div>
            </td>
          </tr>
        )}

        {poolAddress && isConnecting && (
          <tr>
            <td colSpan={8} className="px-6 py-8 text-center">
              <div className="text-neutral-400">
                <RefreshCcw className="animate-spin mx-auto mb-2" size={20} />
                <p className="text-sm">Connecting to trade feed...</p>
              </div>
            </td>
          </tr>
        )}

        {poolAddress && error && (
          <tr>
            <td colSpan={8} className="px-6 py-8 text-center">
              <div className="text-red-400">
                <p className="text-sm">Failed to connect to trade feed</p>
                <p className="text-xs mt-1">{error}</p>
              </div>
            </td>
          </tr>
        )}

        {poolAddress && isConnected && filteredTrades.length === 0 && (
          <tr>
            <td colSpan={8} className="px-6 py-8 text-center">
              <div className="text-neutral-400">
                <p className="text-sm">No trades yet</p>
                <p className="text-xs mt-1">Waiting for trade activity...</p>
              </div>
            </td>
          </tr>
        )}

        {/* Trade Data */}
        {filteredTrades.map(t => (
          <tr key={t.id} className="hover:bg-neutral-800/30 transition-colors text-sm align-middle">
            {/* Age */}
            <td className="px-6 py-4 min-w-[120px] whitespace-nowrap align-middle">
              <div className="flex items-center gap-2 font-mono text-neutral-200">
                {t.age}
                <span className={`px-2 py-0.5 rounded-full border text-xs ${badgeCls(t.trader)}`}>
                  {t.trader}
                </span>
              </div>
            </td>

            {/* Tip & Prio */}
            <td className="px-6 py-4 min-w-[100px] align-middle whitespace-nowrap">
              <div className="space-y-1">
                <div className="font-mono text-neutral-100">{t.tipAndPrio.tip.toFixed(4)} SOL</div>
                <span className={`px-2 py-0.5 rounded-full border text-xs font-medium ${priorityCls(t.tipAndPrio.priority)}`}>
                  {t.tipAndPrio.priority}
                </span>
              </div>
            </td>

            {/* Side */}
            <td className="px-6 py-4 min-w-[80px] align-middle whitespace-nowrap">
              <div className={`flex items-center gap-1 font-semibold ${sideCls(t.side)}`}>
                {sideIcon(t.side)} {t.side}
              </div>
            </td>

            {/* Price */}
            <td className="px-6 py-4 min-w-[100px] align-middle font-mono text-neutral-100 whitespace-nowrap">
              {fmtUsd(t.priceSOL)}
            </td>

            {/* Amount */}
            <td className="px-6 py-4 min-w-[100px] align-middle font-mono text-neutral-300 whitespace-nowrap">
              {t.amount.toFixed(2)} SOL
            </td>

            {/* Total USD */}
            <td className="px-6 py-4 min-w-[120px] align-middle font-mono text-neutral-100 whitespace-nowrap">
              {fmtUsd(t.totalUSD)}
            </td>

            {/* Total SOL */}
            <td className="px-6 py-4 min-w-[100px] align-middle font-mono text-neutral-300 whitespace-nowrap">
              {t.totalSOL.toFixed(2)} SOL
            </td>

            {/* Maker Txn */}
            <td className="px-6 py-4 min-w-[140px] align-middle whitespace-nowrap">
              <div className="font-mono">
                <code className="px-2 py-0.5 rounded border border-blue-500/30 bg-blue-500/10 text-blue-400">
                  {t.makerTxn}
                </code>
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
</div>

  );
};

export default Traders;
