import { useState, useEffect, useCallback, useRef } from 'react';
import { websocketService } from '../services/websocketService';

interface TradeData {
  pair: string;
  date: number;
  token_price: number;
  token_price_vs: number;
  token_amount: number;
  token_amount_vs: number;
  token_amount_usd: number;
  type: 'buy' | 'sell';
  operation: string;
  blockchain: string;
  hash: string;
  sender: string;
  token_amount_raw: string;
  token_amount_raw_vs: string;
  pairData: {
    token0: any;
    token1: any;
    volume24h: number;
    liquidity: number;
    blockchain: string;
    address: string;
    createdAt: string;
    type: string;
    baseToken: string;
    exchange: any;
    factory: string;
    quoteToken: string;
    bonded: boolean;
    price: number;
    priceToken: number;
    priceTokenString: string;
    [key: string]: any;
  };
}

interface TradeDataUpdate {
  poolAddress: string;
  tradeData: TradeData;
  tradeHistory: TradeData[];
  timestamp: number;
}

interface UseTradeWebSocketReturn {
  tradeData: TradeData | null;
  tradeHistory: TradeData[];
  isConnected: boolean;
  isConnecting: boolean;
  lastUpdate: number | null;
  error: string | null;
  subscribeToPool: (poolAddress: string) => void;
  unsubscribeFromPool: (poolAddress: string) => void;
}

// Global trade connection state to avoid multiple connections
interface GlobalTradeConnection {
  isConnected: boolean;
  subscribedPools: Set<string>;
  poolData: Map<string, { tradeData: TradeData | null; tradeHistory: TradeData[]; lastUpdate: number | null }>;
  callbacks: Map<string, Set<(update: TradeDataUpdate) => void>>;
  error: string | null;
  subscriberCount: number;
}

const globalTradeConnection: GlobalTradeConnection = {
  isConnected: false,
  subscribedPools: new Set(),
  poolData: new Map(),
  callbacks: new Map(),
  error: null,
  subscriberCount: 0
};

/**
 * Connect to trade WebSocket if not already connected
 */
const connectGlobalTradeWebSocket = async (): Promise<void> => {
  if (globalTradeConnection.isConnected) {
    console.log('🔌 Trade WebSocket already connected');
    return;
  }

  try {
    console.log('🔌 Connecting to trade WebSocket (global singleton)...');

    if (!websocketService.connected) {
      await websocketService.connect();
    }

    globalTradeConnection.isConnected = true;
    console.log('✅ Global trade WebSocket connected');

  } catch (error: any) {
    globalTradeConnection.error = error.message || 'Connection failed';
    console.error('❌ Failed to connect to trade WebSocket:', error);
    throw error;
  }
};

/**
 * Disconnect from trade WebSocket
 */
const disconnectGlobalTradeWebSocket = (): void => {
  if (!globalTradeConnection.isConnected) {
    return;
  }

  console.log('🔌 Disconnecting from trade WebSocket...');

  // Unsubscribe from all pools
  for (const poolAddress of globalTradeConnection.subscribedPools) {
    websocketService.unsubscribeFromTrade(poolAddress);
  }

  globalTradeConnection.isConnected = false;
  globalTradeConnection.subscribedPools.clear();
  globalTradeConnection.poolData.clear();
  globalTradeConnection.callbacks.clear();
  globalTradeConnection.error = null;
  globalTradeConnection.subscriberCount = 0;

  console.log('✅ Trade WebSocket disconnected');
};

/**
 * Subscribe to trade data for a specific pool
 */
const subscribeToPool = async (poolAddress: string): Promise<void> => {
  if (!poolAddress) {
    console.warn('⚠️ Cannot subscribe to trade data - no pool address provided');
    return;
  }

  try {
    // Ensure WebSocket connection
    if (!globalTradeConnection.isConnected) {
      await connectGlobalTradeWebSocket();
    }

    // Subscribe to pool if not already subscribed
    if (!globalTradeConnection.subscribedPools.has(poolAddress)) {
      await websocketService.subscribeToTrade(poolAddress);
      globalTradeConnection.subscribedPools.add(poolAddress);

      // Initialize pool data
      if (!globalTradeConnection.poolData.has(poolAddress)) {
        globalTradeConnection.poolData.set(poolAddress, {
          tradeData: null,
          tradeHistory: [],
          lastUpdate: null
        });
      }

      console.log(`📊 Subscribed to trade data for pool: ${poolAddress}`);
    }

    // Set up data listener if not already set
    if (!globalTradeConnection.callbacks.has(poolAddress)) {
      globalTradeConnection.callbacks.set(poolAddress, new Set());

      const unsubscribe = websocketService.onTradeData((update: TradeDataUpdate) => {
        if (update.poolAddress === poolAddress) {
          // Update global pool data
          const poolData = globalTradeConnection.poolData.get(poolAddress);
          if (poolData) {
            poolData.tradeData = update.tradeData;
            poolData.tradeHistory = update.tradeHistory;
            poolData.lastUpdate = update.timestamp;
          }

          // Notify all subscribers for this pool
          const callbacks = globalTradeConnection.callbacks.get(poolAddress);
          if (callbacks) {
            callbacks.forEach(callback => {
              callback(update);
            });
          }
        }
      });

      // Store unsubscribe function for cleanup
      (globalTradeConnection as any)[`unsubscribe_${poolAddress}`] = unsubscribe;
    }

  } catch (error: any) {
    globalTradeConnection.error = error.message || 'Subscription failed';
    console.error(`❌ Failed to subscribe to trade data for pool ${poolAddress}:`, error);
    throw error;
  }
};

/**
 * Unsubscribe from trade data for a specific pool
 */
const unsubscribeFromPool = (poolAddress: string): void => {
  if (!poolAddress || !globalTradeConnection.subscribedPools.has(poolAddress)) {
    return;
  }

  try {
    websocketService.unsubscribeFromTrade(poolAddress);
    globalTradeConnection.subscribedPools.delete(poolAddress);

    // Clean up callbacks
    const callbacks = globalTradeConnection.callbacks.get(poolAddress);
    if (callbacks) {
      callbacks.clear();
      globalTradeConnection.callbacks.delete(poolAddress);
    }

    // Clean up unsubscribe function
    const unsubscribeKey = `unsubscribe_${poolAddress}`;
    const unsubscribeFn = (globalTradeConnection as any)[unsubscribeKey];
    if (unsubscribeFn) {
      unsubscribeFn();
      delete (globalTradeConnection as any)[unsubscribeKey];
    }

    // Remove pool data
    globalTradeConnection.poolData.delete(poolAddress);

    console.log(`📊 Unsubscribed from trade data for pool: ${poolAddress}`);

    // Disconnect if no more subscriptions
    if (globalTradeConnection.subscribedPools.size === 0 && globalTradeConnection.subscriberCount === 0) {
      disconnectGlobalTradeWebSocket();
    }

  } catch (error: any) {
    console.error(`❌ Failed to unsubscribe from trade data for pool ${poolAddress}:`, error);
  }
};

export const useTradeWebSocket = (poolAddress?: string): UseTradeWebSocketReturn => {
  const [tradeData, setTradeData] = useState<TradeData | null>(null);
  const [tradeHistory, setTradeHistory] = useState<TradeData[]>([]);
  const [isConnected, setIsConnected] = useState(globalTradeConnection.isConnected);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(globalTradeConnection.error);
  const [currentPoolAddress, setCurrentPoolAddress] = useState<string | null>(poolAddress || null);

  const componentId = useRef<string>(`trade_component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  // Data update callback for this component
  const handleTradeUpdate = useCallback((update: TradeDataUpdate) => {
    console.log(`📊 Component ${componentId.current} received trade data update for pool: ${update.poolAddress}`);
    setTradeData(update.tradeData);
    setTradeHistory(update.tradeHistory);
    setLastUpdate(update.timestamp);
    setError(null);
    setIsConnecting(false);
    setIsConnected(true);
  }, []);

  // Subscribe to pool function
  const subscribeToPoolFn = useCallback(async (poolAddr: string) => {
    if (!poolAddr) {
      console.warn('⚠️ Cannot subscribe - no pool address provided');
      return;
    }

    if (currentPoolAddress === poolAddr) {
      console.log(`📊 Already subscribed to pool: ${poolAddr}`);
      return;
    }

    try {
      setIsConnecting(true);
      setError(null);

      // Unsubscribe from previous pool if any
      if (currentPoolAddress) {
        const callbacks = globalTradeConnection.callbacks.get(currentPoolAddress);
        if (callbacks) {
          callbacks.delete(handleTradeUpdate);
        }
        
        // Only unsubscribe if no other components are using this pool
        if (callbacks && callbacks.size === 0) {
          unsubscribeFromPool(currentPoolAddress);
        }
      }

      // Subscribe to new pool
      await subscribeToPool(poolAddr);

      // Add this component as a subscriber
      const callbacks = globalTradeConnection.callbacks.get(poolAddr);
      if (callbacks) {
        callbacks.add(handleTradeUpdate);
      }

      // Set initial state from global connection
      const poolData = globalTradeConnection.poolData.get(poolAddr);
      if (poolData) {
        setTradeData(poolData.tradeData);
        setTradeHistory(poolData.tradeHistory);
        setLastUpdate(poolData.lastUpdate);
      }

      setCurrentPoolAddress(poolAddr);
      setIsConnected(globalTradeConnection.isConnected);
      setIsConnecting(false);

      globalTradeConnection.subscriberCount++;

    } catch (error: any) {
      setIsConnecting(false);
      setError(error.message || 'Subscription failed');
      console.error(`❌ Failed to subscribe to pool ${poolAddr}:`, error);
    }
  }, [currentPoolAddress, handleTradeUpdate]);

  // Unsubscribe from pool function
  const unsubscribeFromPoolFn = useCallback(() => {
    if (!currentPoolAddress) return;

    try {
      // Remove this component as a subscriber
      const callbacks = globalTradeConnection.callbacks.get(currentPoolAddress);
      if (callbacks) {
        callbacks.delete(handleTradeUpdate);
        
        // Only unsubscribe if no other components are using this pool
        if (callbacks.size === 0) {
          unsubscribeFromPool(currentPoolAddress);
        }
      }

      globalTradeConnection.subscriberCount--;
      setCurrentPoolAddress(null);
      setTradeData(null);
      setTradeHistory([]);
      setLastUpdate(null);
      setIsConnected(false);

    } catch (error: any) {
      console.error(`❌ Failed to unsubscribe from pool ${currentPoolAddress}:`, error);
    }
  }, [currentPoolAddress, handleTradeUpdate]);

  // Effect to handle pool address changes
  useEffect(() => {
    if (poolAddress && poolAddress !== currentPoolAddress) {
      subscribeToPoolFn(poolAddress);
    }
  }, [poolAddress, currentPoolAddress, subscribeToPoolFn]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentPoolAddress) {
        const callbacks = globalTradeConnection.callbacks.get(currentPoolAddress);
        if (callbacks) {
          callbacks.delete(handleTradeUpdate);
          globalTradeConnection.subscriberCount--;
          
          // Only unsubscribe if no other components are using this pool
          if (callbacks.size === 0) {
            unsubscribeFromPool(currentPoolAddress);
          }
        }
      }
    };
  }, [currentPoolAddress, handleTradeUpdate]);

  return {
    tradeData,
    tradeHistory,
    isConnected,
    isConnecting,
    lastUpdate,
    error,
    subscribeToPool: subscribeToPoolFn,
    unsubscribeFromPool: unsubscribeFromPoolFn
  };
};
