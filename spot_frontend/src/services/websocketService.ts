import { io, Socket } from 'socket.io-client';
import { activityService } from './activityService';

interface PulseDataUpdate {
  data: any;
  timestamp: number;
  source: 'websocket' | 'api';
}

interface ConnectionStatus {
  connected: boolean;
  clientId?: string;
  timestamp: number;
}

interface RoomStats {
  totalClients: number;
  pulseRoomClients: number;
  timestamp: number;
}

type PulseDataCallback = (data: PulseDataUpdate) => void;
type ConnectionStatusCallback = (status: ConnectionStatus) => void;
type RoomStatsCallback = (stats: RoomStats) => void;
type ErrorCallback = (error: any) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private isConnecting = false;
  private isRegistered = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10; // Increased from 5
  private reconnectDelay = 2000; // Increased from 1000ms
  private healthCheckRetries = 0;
  private maxHealthCheckRetries = 3;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isOnPulsePage = false;

  // Event callbacks
  private pulseDataCallbacks: Set<PulseDataCallback> = new Set();
  private connectionStatusCallbacks: Set<ConnectionStatusCallback> = new Set();
  private roomStatsCallbacks: Set<RoomStatsCallback> = new Set();
  private errorCallbacks: Set<ErrorCallback> = new Set();

  constructor() {
    this.setupBeforeUnload();
  }

  /**
   * Check if WebSocket server is ready
   */
  private async checkWebSocketHealth(): Promise<boolean> {
    try {
      const response = await fetch('/api/websocket/health');
      const data = await response.json();
      return response.ok && data.ready;
    } catch (error) {
      console.warn('WebSocket health check failed:', error);
      return false;
    }
  }

  /**
   * Connect to the WebSocket server with health checking
   */
  public async connect(): Promise<void> {
    if (this.isConnecting || this.isConnected) {
      console.log('WebSocket already connecting or connected');
      return;
    }

    this.isConnecting = true;

    try {
      // First check if WebSocket server is ready
      console.log('🔍 Checking WebSocket server health...');
      const isHealthy = await this.checkWebSocketHealth();

      if (!isHealthy) {
        console.log('⚠️ WebSocket server not ready, will retry...');
        this.healthCheckRetries++;

        if (this.healthCheckRetries <= this.maxHealthCheckRetries) {
          this.isConnecting = false;
          setTimeout(() => {
            this.connect().catch(error => {
              console.error('Health check retry failed:', error);
            });
          }, 3000); // Wait 3 seconds before retrying health check
          return;
        } else {
          console.warn('⚠️ Max health check retries reached, attempting connection anyway...');
          this.healthCheckRetries = 0; // Reset for next connection attempt
        }
      } else {
        console.log('✅ WebSocket server is healthy');
        this.healthCheckRetries = 0; // Reset on successful health check
      }

      const session = activityService.getOrCreateSession();
      const socketUrl = this.getSocketUrl();

      console.log(`🔌 Connecting to WebSocket server: ${socketUrl}`);
      console.log(`🔧 Environment: ${import.meta.env.MODE}`);
      console.log(`🔧 Hostname: ${window.location.hostname}`);
      console.log(`🔧 Protocol: ${window.location.protocol}`);

      this.socket = io(socketUrl, {
        transports: ['websocket', 'polling'],
        timeout: 15000, // Increased timeout
        reconnection: false, // We'll handle reconnection manually
        forceNew: true,
        upgrade: true,
        rememberUpgrade: false
      });

      this.setupEventHandlers();

      // Wait for connection
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 15000); // Increased timeout to match socket timeout

        this.socket!.on('connect', () => {
          clearTimeout(timeout);
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.healthCheckRetries = 0; // Reset health check retries on successful connection

          console.log('✅ Connected to WebSocket server');

          // Register with the server
          this.socket!.emit('register', {
            userId: session.userId,
            sessionId: session.sessionId
          });

          this.startHeartbeat();
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout);
          this.isConnecting = false;
          console.error('❌ WebSocket connection error:', error);
          reject(error);
        });
      });

    } catch (error) {
      this.isConnecting = false;
      console.error('Failed to connect to WebSocket:', error);
      this.handleReconnect();
      throw error;
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    console.log('🔌 Disconnecting from WebSocket server');

    this.stopHeartbeat();

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.isRegistered = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Join the pulse room for real-time pulse data
   */
  public async joinPulseRoom(): Promise<void> {
    if (!this.isConnected || this.isOnPulsePage) {
      if (!this.isConnected) {
        await this.connect();
      }
      if (this.isOnPulsePage) {
        console.log('Already in pulse room');
        return;
      }
    }

    // Wait for registration to complete
    if (!this.isRegistered) {
      console.log('⏳ Waiting for registration to complete...');
      await new Promise<void>((resolve) => {
        const checkRegistration = () => {
          if (this.isRegistered) {
            resolve();
          } else {
            setTimeout(checkRegistration, 100);
          }
        };
        checkRegistration();
      });
    }

    const session = activityService.getOrCreateSession();

    this.socket!.emit('join-pulse', {
      userId: session.userId,
      sessionId: session.sessionId
    });

    this.isOnPulsePage = true;
    console.log('🎯 Joined pulse room');
  }

  /**
   * Leave the pulse room
   */
  public leavePulseRoom(): void {
    if (!this.isConnected || !this.isOnPulsePage) {
      return;
    }

    const session = activityService.getOrCreateSession();
    
    this.socket!.emit('leave-pulse', {
      userId: session.userId,
      sessionId: session.sessionId
    });

    this.isOnPulsePage = false;
    console.log('🚪 Left pulse room');
  }



  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connection-status', (status: ConnectionStatus) => {
      console.log('📡 Connection status:', status);
      this.isRegistered = true; // Registration confirmed
      this.connectionStatusCallbacks.forEach(callback => callback(status));
    });

    this.socket.on('pulse-data', (update: PulseDataUpdate) => {
      console.log('📥 Received pulse data update:', {
        source: update.source,
        timestamp: new Date(update.timestamp).toLocaleTimeString(),
        dataKeys: Object.keys(update.data || {})
      });
      this.pulseDataCallbacks.forEach(callback => callback(update));
    });



    this.socket.on('room-stats', (stats: RoomStats) => {
      console.log('📊 Room stats:', stats);
      this.roomStatsCallbacks.forEach(callback => callback(stats));
    });

    this.socket.on('heartbeat-ack', (data: { timestamp: number }) => {
      console.debug('💓 Heartbeat acknowledged:', new Date(data.timestamp).toLocaleTimeString());
    });

    this.socket.on('error', (error: any) => {
      console.error('❌ WebSocket error:', error);
      this.errorCallbacks.forEach(callback => callback(error));
    });

    this.socket.on('disconnect', (reason: string) => {
      console.warn('🔌 WebSocket disconnected:', reason);
      this.isConnected = false;
      this.isRegistered = false;
      this.isOnPulsePage = false;
      this.stopHeartbeat();

      // Attempt to reconnect unless it was a manual disconnect
      if (reason !== 'io client disconnect') {
        this.handleReconnect();
      }
    });
  }

  /**
   * Start sending heartbeats
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();

    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.socket) {
        const session = activityService.getOrCreateSession();
        this.socket.emit('heartbeat', {
          userId: session.userId,
          sessionId: session.sessionId
        });
      }
    }, 30000); // Send heartbeat every 30 seconds

    console.log('💓 Started WebSocket heartbeat');
  }

  /**
   * Stop sending heartbeats
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
      console.log('💓 Stopped WebSocket heartbeat');
    }
  }

  /**
   * Handle reconnection with exponential backoff and health checking
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`❌ Max reconnection attempts (${this.maxReconnectAttempts}) reached`);
      return;
    }

    this.reconnectAttempts++;
    // Cap the delay at 30 seconds
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);

    console.log(`🔄 Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  /**
   * Get the WebSocket server URL
   */
  private getSocketUrl(): string {
    const hostname = window.location.hostname;
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

    // Production environment
    if (hostname.includes('crypfi.io')) {
      return 'wss://redfyn.crypfi.io';
    }

    // Production server IP
    if (hostname === '*************') {
      return `${protocol}//*************:5001`;
    }

    // Development environment - check for localhost
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      // Use the backend port directly for development
      const backendPort = import.meta.env.VITE_BACKEND_PORT || '5001';
      return `${protocol}//localhost:${backendPort}`;
    }

    // Fallback for other environments
    const apiUrl = import.meta.env.VITE_API_URL || 'https://redfyn.lrbinfotech.com';
    return apiUrl.replace(/^https?:/, protocol);
  }

  /**
   * Setup beforeunload handler
   */
  private setupBeforeUnload(): void {
    window.addEventListener('beforeunload', () => {
      if (this.isOnPulsePage) {
        this.leavePulseRoom();
      }
      this.disconnect();
    });
  }

  // Event subscription methods
  public onPulseData(callback: PulseDataCallback): () => void {
    this.pulseDataCallbacks.add(callback);
    return () => this.pulseDataCallbacks.delete(callback);
  }

  public onConnectionStatus(callback: ConnectionStatusCallback): () => void {
    this.connectionStatusCallbacks.add(callback);
    return () => this.connectionStatusCallbacks.delete(callback);
  }

  public onRoomStats(callback: RoomStatsCallback): () => void {
    this.roomStatsCallbacks.add(callback);
    return () => this.roomStatsCallbacks.delete(callback);
  }

  public onError(callback: ErrorCallback): () => void {
    this.errorCallbacks.add(callback);
    return () => this.errorCallbacks.delete(callback);
  }

  // Getters
  public get connected(): boolean {
    return this.isConnected;
  }

  public get connecting(): boolean {
    return this.isConnecting;
  }

  public get inPulseRoom(): boolean {
    return this.isOnPulsePage;
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
