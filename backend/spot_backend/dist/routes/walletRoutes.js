import express from 'express';
import { getWalletBalancesByPairs, identifyWalletType, getTokenPairs, getAllWalletBalances, scanAllWalletTokens, discoverAllTokens } from '../controllers/walletController.js';
import { getPrivySolanaWalletInfo, unlinkWallet, getUserAccountDetails } from '../controllers/privyController.js';
const router = express.Router();
/**
 * @route POST /api/wallet/balances
 * @desc Get wallet balances by token pairs
 * @access Public
 */
router.post('/balances', getWalletBalancesByPairs);
/**
 * @route POST /api/wallet/identify
 * @desc Identify wallet type (Ethereum or Solana)
 * @access Public
 */
router.post('/identify', identifyWalletType);
/**
 * @route POST /api/wallet/token-pairs
 * @desc Get token pairs from DexScreener
 * @access Public
 */
router.post('/token-pairs', getTokenPairs);
/**
 * @route POST /api/wallet/all-balances
 * @desc Get wallet balances across all supported networks (ETH, BSC, Solana)
 * @access Public
 */
router.post('/all-balances', getAllWalletBalances);
/**
 * @route POST /api/wallet/scan-all-tokens
 * @desc Scan for all tokens in a wallet (using blockchain explorer APIs)
 * @access Public
 */
router.post('/scan-all-tokens', scanAllWalletTokens);
/**
 * @route POST /api/wallet/discover-tokens
 * @desc Comprehensive token discovery across multiple chains
 * @access Public
 */
router.post('/discover-tokens', discoverAllTokens);
/**
 * @route POST /api/wallet/privy-solana-info
 * @desc Get Solana wallet information from Privy API
 * @access Public
 */
router.post('/privy-solana-info', getPrivySolanaWalletInfo);
/**
 * @route POST /api/wallet/unlink-wallet
 * @desc Unlink a specific wallet from a Privy user
 * @access Public
 */
router.post('/unlink-wallet', unlinkWallet);
/**
 * @route POST /api/wallet/user-account-details
 * @desc Get detailed user account information for debugging
 * @access Public
 */
router.post('/user-account-details', getUserAccountDetails);
export default router;
//# sourceMappingURL=walletRoutes.js.map