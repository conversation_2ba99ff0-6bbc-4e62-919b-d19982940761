{"name": "spot_backend", "version": "1.0.0", "description": "Backend for a spot trading application", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec \"node --loader ts-node/esm\" src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "test-websocket": "node test-websocket.js", "test:monitoring": "node --loader ts-node/esm scripts/test-monitoring-service.js"}, "keywords": ["spot", "trading", "crypto", "exchange"], "author": "", "license": "ISC", "dependencies": {"@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@metaplex-foundation/umi": "^1.2.0", "@metaplex-foundation/umi-bundle-defaults": "^1.2.0", "@solana/spl-token": "^0.3.11", "@solana/web3.js": "^1.87.6", "@supabase/supabase-js": "^2.50.0", "@types/ws": "^8.18.0", "axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "ethers": "^5.7.2", "express": "^4.18.2", "node-cron": "^3.0.3", "redis": "^4.7.0", "socket.io": "^4.8.1", "ws": "^8.18.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.11.25", "@types/node-cron": "^3.0.11", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.1", "typescript": "^5.4.2"}}