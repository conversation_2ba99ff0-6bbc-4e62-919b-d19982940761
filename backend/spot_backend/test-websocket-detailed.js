const WebSocket = require('ws');

// Test different WebSocket endpoints and message formats
const testConfigurations = [
  {
    name: 'Pulse Stream (Correct Format)',
    url: 'wss://api.mobula.io',
    message: {
      type: 'pulse',
      authorization: 'fffa68cd-6bde-4ac5-909d-eb627d8baca0',
      payload: {
        factories: ['pumpfun'],
        blockchains: ['solana:solana']
      }
    }
  },
  {
    name: 'Market Feed Test',
    url: 'wss://api.mobula.io',
    message: {
      type: 'market',
      authorization: 'fffa68cd-6bde-4ac5-909d-eb627d8baca0',
      payload: {
        assets: [{ name: 'Bitcoin' }],
        interval: 15
      }
    }
  }
];

const testWebSocketConfig = (config, index) => {
  return new Promise((resolve) => {
    console.log(`\n🔍 Test ${index + 1}: ${config.name}`);
    console.log(`📡 Connecting to: ${config.url}`);
    
    const ws = new WebSocket(config.url);
    let messageCount = 0;
    let hasReceivedData = false;
    
    const timeout = setTimeout(() => {
      console.log(`⏰ Test ${index + 1} timeout reached`);
      ws.close();
    }, 15000); // 15 seconds per test
    
    ws.on('open', () => {
      console.log(`✅ Test ${index + 1}: WebSocket connected`);
      console.log(`📤 Sending message:`, JSON.stringify(config.message, null, 2));
      ws.send(JSON.stringify(config.message));
    });
    
    ws.on('message', (data) => {
      messageCount++;
      hasReceivedData = true;
      
      try {
        const message = JSON.parse(data.toString());
        console.log(`📥 Test ${index + 1}: Message ${messageCount}:`, {
          type: message.type,
          hasData: !!message.data,
          hasError: !!message.error,
          keys: Object.keys(message),
          timestamp: new Date().toISOString()
        });
        
        // Log error messages in full
        if (message.error) {
          console.log(`❌ Test ${index + 1}: Error received:`, message.error);
        }
        
        // Log success messages
        if (message.type === 'success' || message.status === 'success') {
          console.log(`✅ Test ${index + 1}: Success message:`, message);
        }
        
        // Log first data message in full
        if (messageCount === 1) {
          console.log(`📋 Test ${index + 1}: Full first message:`, JSON.stringify(message, null, 2));
        }
        
      } catch (error) {
        console.error(`❌ Test ${index + 1}: Error parsing message:`, error);
        console.log(`📄 Test ${index + 1}: Raw message:`, data.toString());
      }
    });
    
    ws.on('error', (error) => {
      console.error(`❌ Test ${index + 1}: WebSocket error:`, error.message);
    });
    
    ws.on('close', (code, reason) => {
      clearTimeout(timeout);
      console.log(`🔌 Test ${index + 1}: Closed - Code: ${code}, Reason: ${reason.toString()}`);
      console.log(`📊 Test ${index + 1}: Messages received: ${messageCount}`);
      console.log(`📈 Test ${index + 1}: Data received: ${hasReceivedData ? 'Yes' : 'No'}`);
      resolve({ messageCount, hasReceivedData, config: config.name });
    });
  });
};

const runAllTests = async () => {
  console.log('🚀 Starting Comprehensive Mobula WebSocket Tests');
  console.log('⏱️  Each test will run for 15 seconds');
  console.log('🎯 Testing different endpoints and message formats');
  console.log('');
  
  const results = [];
  
  for (let i = 0; i < testConfigurations.length; i++) {
    const result = await testWebSocketConfig(testConfigurations[i], i);
    results.push(result);
    
    // Wait 2 seconds between tests
    if (i < testConfigurations.length - 1) {
      console.log('\n⏳ Waiting 2 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('\n📋 Test Summary:');
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.config}: ${result.messageCount} messages, Data: ${result.hasReceivedData ? 'Yes' : 'No'}`);
  });
  
  const workingTests = results.filter(r => r.hasReceivedData);
  if (workingTests.length > 0) {
    console.log(`\n✅ ${workingTests.length} test(s) received data successfully!`);
  } else {
    console.log('\n❌ No tests received data. Possible issues:');
    console.log('   - API key may not have required plan (Growth/Enterprise)');
    console.log('   - WebSocket endpoint may have changed');
    console.log('   - Message format may be incorrect');
    console.log('   - Network/firewall issues');
  }
};

runAllTests().catch(console.error);
